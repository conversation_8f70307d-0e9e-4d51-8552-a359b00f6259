import { Component, OnInit, Inject } from '@angular/core';
declare let $: any;
import { UserIdleService } from 'angular-user-idle';
import { interval, Subject, Subscription } from 'rxjs';
import { tokenRefreshTime, userIdleTime, userIdleTimeOut } from './config';
import { CommonService } from './services/common/common.service';
import { Router, RoutesRecognized } from '@angular/router';
import { MsalBroadcastService, MsalGuardConfiguration, MsalService, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { DEFAULT_INTERRUPTSOURCES, Idle } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { DataService } from './services/common/data.service';
import { InteractionType, PopupRequest, AuthenticationResult, RedirectRequest, EventMessage, EventType } from '@azure/msal-browser';
import { mobiscroll } from '@mobiscroll/angular-lite';
import { filter, pairwise, takeUntil } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styles: []
})
export class AppComponent implements OnInit {
  public subscription: Subscription;
  public userName: string;
  public pageName = '';
  public routeName = "";
  public userAccess: any = {};

  loadAPI: Promise<any>;
  isToggled = false;
  isIframe = false;
  loggedIn = false;
  private readonly _destroying$ = new Subject<void>();
  private currentUrl = '';
  public baseWebUrl = '';
  errorCode = '';
  idleState = 'Not started.';
  timedOut = false;
  lastPing: Date = new Date();
  device = false;
  idleTime = 0;
  msalCount = 0;
  pingCount = 0;
  public userIdleTime = 0;
  public userIdleTimeStarted = 30;
  public subscriptions: Subscription;

  asyncLocalStorage = {
    setItem(key, value) {
      return Promise.resolve().then(() => {
        sessionStorage.setItem(key, value);
      });
    },
    getItem(key) {
      return Promise.resolve().then(() => {
        return sessionStorage.getItem(key);
      });
    }
  };
  accessToken = '';
  visiblity = true;
  message: string;
  previousUrl: string;
  public year:string;
  timestamp:any;
  constructor(
    private readonly commonServ: CommonService,
    private readonly router: Router,
    @Inject(MSAL_GUARD_CONFIG) private readonly msalGuardConfig: MsalGuardConfiguration,
    private readonly authService: MsalService,
    private readonly msalBroadcastService: MsalBroadcastService,
    private readonly data: DataService,
    private readonly userIdle: UserIdleService,
    private readonly idle: Idle,
    private readonly keepalive: Keepalive,
    private readonly datepipe: DatePipe,
    private deviceService: DeviceDetectorService
  ) {
    this.device = deviceService.isMobile();
    this.currentUrl = window.location.href;
    this.baseWebUrl = window.location.origin + '/#/';
    this.year=this.datepipe.transform(new Date(), 'yyyy')!!;
    this.checkAccount();
    let token = this.commonServ.extractMSALToken();
    if (this.loggedIn && token && this.baseWebUrl != this.currentUrl) {
      this.data.updateAcessKey(token);
      this.data.updatenewURL(this.currentUrl);
      sessionStorage.removeItem('loaded');
      this.router.navigate([''], { state: { url: this.currentUrl, token } });
    }

    else if (this.baseWebUrl != this.currentUrl && sessionStorage.getItem('url') != this.currentUrl) {
      this.getPreviousURL();
    }

    document.addEventListener(
      'visibilitychange'
      , () => {
        if (document.hidden) {
          this.visiblity = false;
        } else {
          this.visiblity = true;
        }
      }
    );

  }

  ngOnInit() {
    $('#loading').hide();
    this.device = this.deviceService.isMobile();
    this.isIframe = window !== window.parent && !window.opener;
    this.subscription = this.data.userDetails.subscribe(user => {
      this.userName = user;
      if (user) {
        this.userIdleStart();
      }
    });
    this.subscription = this.data.accessKey.subscribe(key => this.accessToken = key);
    this.subscription = this.data.isLoggedIn.subscribe(status => this.loggedIn = status);
    this.subscription = this.data.currentUpateURL.subscribe(url => this.currentUrl = url);
    this.subscription = this.data.isUserAccess.subscribe(access => this.userAccess = access);

    this.checkAccount();

    this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => msg.eventType === EventType.LOGIN_SUCCESS || msg.eventType === EventType.ACQUIRE_TOKEN_SUCCESS),
        takeUntil(this._destroying$)
      )
      .subscribe((result) => {
        this.checkAccount();
      });
  }

  loadPageName(pName, rName) {
    this.pageName = pName;
    this.routeName = rName;
  }

  sidebarToggle() {

    $("body").toggleClass("sidebar-toggled");
    $(".sidebar").toggleClass("toggled");
    $(".sidebar").hasClass("toggled");
    $(".sidebar .collapse").collapse("hide");

    $(window).resize(() => {
      if ($(window).width() < 768) {
        $(".sidebar .collapse").collapse("hide");
      }
      if ($(window).width() < 480 && !$(".sidebar").hasClass("toggled")) {
        $("body").addClass("sidebar-toggled");
        $(".sidebar").addClass("toggled");
        $(".sidebar .collapse").collapse("hide");
      }
    });

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && window.innerWidth <= 575) {
      this.isToggled = true;
    }
    else {
      this.isToggled = $('#accordionSidebar').hasClass('toggled');
    }

    this.addTogglesForChilds('#physicianTab');
    this.addTogglesForChilds('#coordinatorTab');
    this.addTogglesForChilds('#billingTab');
    this.addTogglesForChilds('#reportsTab');
    this.addTogglesForChilds('#usermanagementTab');
  }

  addTogglesForChilds(parentId) {
    if (this.isToggled) {
      $(parentId + ' .collapse-inner a').attr('data-toggle', 'collapse');
      $(parentId + ' .collapse-inner a').attr('data-target', parentId);
    }
    else {
      $(parentId + ' .collapse-inner a').removeAttr('data-toggle');
      $(parentId + ' .collapse-inner a').removeAttr('data-target');
    }
    this.checkURL();
  }

  mobileMenuTogle() {
    $('#menu-loading').show();
    $("#myDropdown").toggle("show");
  }

  mobileMenuClose() {
    $('#menu-loading').hide();
    $("#myDropdown").toggle("show");
  }

  checkAccount() {
    this.loggedIn = this.authService.instance.getAllAccounts().length > 0;
    this.data.updateLogginStatus(this.loggedIn);
  }

  login() {
    $('#loading').show();
    if (this.msalGuardConfig.interactionType === InteractionType.Popup) {
      if (this.msalGuardConfig.authRequest) {
        this.authService.loginPopup({ ...this.msalGuardConfig.authRequest } as PopupRequest)
          .subscribe((response: AuthenticationResult) => {
            this.authService.instance.setActiveAccount(response.account);
            this.checkAccount();
          });
      } else {
        this.authService.loginPopup()
          .subscribe((response: AuthenticationResult) => {
            this.authService.instance.setActiveAccount(response.account);
            this.checkAccount();
          });
      }
    } else if (this.msalGuardConfig.authRequest) {
      this.authService.loginRedirect({ ...this.msalGuardConfig.authRequest } as RedirectRequest);
    } else {
      this.authService.loginRedirect();
    }
  }

  logout() {
    this.commonServ.logout().subscribe((p: any) => {
      this.clearSession();
    }, error => {
      console.log(error);
      this.clearSession();
    });
  }

  ngOnDestroy(): void {
    this._destroying$.next();
    this._destroying$.complete();
    this.subscription.unsubscribe();
    this.router.navigate([''], { replaceUrl: true });
  }

  onActivate(event) {
    window.scroll(0, 0);
  }

  userIdleStart() {
    console.log("idle start")
    if (this.loggedIn) {
      interval(tokenRefreshTime);
    }

    if (!(/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) && this.loggedIn) {
      //this.device = false;
      // Start watching for user inactivity.
      this.userIdle.startWatching();

      // Start watching when user idle is starting.
      this.userIdle.onTimerStart().subscribe(count => {
        if (this.visiblity && this.idleState != 'Idle Now!') {
          this.userIdleTime = count;
          $('#sessExpModel').modal('show');
        }
        else {
          this.idleState = 'Idle Now!';
        }
      });

      // Start watch when time is up.
      this.userIdle.onTimeout().subscribe(() => {
        if (this.visiblity) {
          this.stopLogout();
          this.logout();
        }
        else {
          this.idleState = 'Timed out!';
        }

      });

      // to perform some action periodically every n-minutes in lifecycle of timer (from start timer to timeout).
      this.userIdle.ping$.subscribe(() => {
        const pingNow: any = new Date();

        let lastPing: Date = localStorage.getItem('lastPing') ? new Date(localStorage.getItem('lastPing')!) : pingNow;

        this.pingCount++;

        this.idleTime = this.diff_minutes(lastPing);

        if (this.idleTime > 0 && this.idleState == 'Idle Now!') {

          history.pushState({}, '', '/#/signin');
          this.router.navigate(['/signin']);
        }
        else if (this.visiblity) {
          const pingNow: any = new Date();
          localStorage.setItem('lastPing', pingNow);
        }

      });
    }

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && this.loggedIn) {
      //this.device = true;
      // sets an idle timeout in seconds
      this.idle.setIdle(userIdleTime);
      // sets a timeout period in seconds
      this.idle.setTimeout(userIdleTimeOut);
      // sets the default interrupts, in this case, things like clicks, scrolls, touches to the document
      this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);

      this.idle.onIdleEnd.subscribe(() => this.idleState = 'No longer idle.');

      // For iPhone devices, show the modal instead of toast
      if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
        
        this.idle.onIdleStart.subscribe(() => {
          this.idleState = 'You\'ve gone idle!';
          $('#mobileSessExpModel').modal('show');
        });

        // Update the countdown timer
        this.idle.onTimeoutWarning.subscribe((countdown) => {
          this.idleState = 'You will time out in ' + countdown + ' seconds!';
          this.userIdleTime = countdown;
          
          setTimeout(() => {
            
          }, 0);
        });

        this.idle.onTimeout.subscribe(() => {
          this.idleState = 'Timed out!';
          this.timedOut = true;
          this.stopMobileLogout();
          this.logout();
        });
      } else {
        
        this.idle.onTimeout.subscribe(() => {
          this.idleState = 'Timed out!';
          this.timedOut = true;
          mobiscroll.toast({
            message: this.idleState,
            display: 'center'
          });
          this.logout();
        });
        this.idle.onIdleStart.subscribe(() => this.idleState = 'You\'ve gone idle!');
        this.idle.onTimeoutWarning.subscribe((countdown) => this.idleState = 'You will time out in ' + countdown + ' seconds!');
      }

      // sets the ping interval to 5 seconds
      this.keepalive.interval(5);

      this.keepalive.onPing.subscribe(() => {
        this.idleTime = this.diff_minutes(this.lastPing);
        if (this.idleTime > 5 && (this.idleState == 'Timed out!' || this.idleState == 'Started.')) {
          this.logout();
        }
        else {
          this.lastPing = new Date();
        }
      });

      this.reset();
    }
  }
  stopLogout() {
    this.userIdle.resetTimer();
    $('#sessExpModel').modal('hide');
    this.idleState = 'Started.';
    this.timedOut = false;
  }

  stopMobileLogout() {
    this.idle.watch();
    $('#mobileSessExpModel').modal('hide');
    this.idleState = 'Started.';
    this.timedOut = false;
  }
  reset() {
    this.idle.watch();
    this.idleState = 'Started.';
    this.timedOut = false;
  }

  diff_minutes(startDate: Date): number {
    // Do your operations
    const endDate = new Date();
    const seconds = (endDate.getTime() - startDate.getTime()) / 1000;
    const minutes = seconds / 60;
    return minutes;
  }

  clearSession() {
    const aMinuteAgo: any = new Date(Date.now() - 2000 * 60);
    localStorage.setItem('lastPing', aMinuteAgo);
    sessionStorage.removeItem('url');
    sessionStorage.removeItem('userPermissions');
    sessionStorage.removeItem('loaded');
    $('#loading').hide();
    this.authService.logout();
    history.pushState({}, '', '/#/');
  }


  getPreviousURL() {
    this.subscription = this.router.events
      .pipe(filter((e: any) => e instanceof RoutesRecognized),
        pairwise()
      ).subscribe((e: any) => {
        this.previousUrl = this.baseWebUrl + '' + e[0].urlAfterRedirects;
        const re = /\/\//gi;
        const url = this.previousUrl.replace(re, '/');
        if (!sessionStorage.getItem('url') && url != this.baseWebUrl && !this.currentUrl) {
          this.data.updatenewURL(url);

          this.asyncLocalStorage.setItem('url', url).then(() => {
            return this.asyncLocalStorage.getItem('url');
          }).then(() => {
            sessionStorage.removeItem('loaded');
            this.router.navigate([''], { state: { url: this.currentUrl, token: this.accessToken } });
          }, error => {
            console.error(error);
          });
        }
      });
  }

  checkURL() {
    if (sessionStorage.getItem('url')) {
      sessionStorage.removeItem('url');
    }
  }
  applyUpdate() {
    localStorage.setItem('appTimestamp', this.timestamp);
    window.location.reload();
  }
}
